# Testing Documentation

This directory contains comprehensive tests for the Portfolio Simulator project.

## Test Structure

### `test_asset.py`
Comprehensive tests for the `Asset` class and `AssetData` dataclass, including:

- **AssetData Tests**: Test the dataclass creation with minimal and full parameters
- **Asset Initialization Tests**: Test Asset object creation with and without quantities
- **Cache Operations Tests**: Test reading from and writing to the JSON cache
- **YFinance Integration Tests**: Test fetching data from Yahoo Finance API
- **Data Flow Tests**: Test the complete data retrieval flow (cache → yfinance → cache)
- **Integration Tests**: Test real file operations using temporary directories

## Running Tests

### Using Poetry (Recommended)
```bash
# Run all tests
poetry run pytest tests/ -v

# Run specific test file
poetry run pytest tests/test_asset.py -v

# Run with coverage (if coverage is installed)
poetry run pytest tests/ --cov=utils --cov-report=html
```

### Using the Test Runner Script
```bash
python run_tests.py
```

### Manual pytest
```bash
# If you have pytest installed globally
pytest tests/ -v
```

## Test Coverage

The current test suite covers:

- ✅ AssetData dataclass creation and validation
- ✅ Asset initialization with various parameters
- ✅ Cache file operations (read/write)
- ✅ YFinance API integration (with mocking)
- ✅ Error handling for network failures
- ✅ Data flow between cache and API
- ✅ File system operations with temporary directories

## Test Dependencies

The tests use the following testing libraries:
- `pytest`: Main testing framework
- `pytest-mock`: Enhanced mocking capabilities
- `unittest.mock`: Python's built-in mocking library

## Mocking Strategy

The tests extensively use mocking to:
- Isolate units under test
- Avoid external API calls during testing
- Test error conditions that are hard to reproduce
- Ensure tests run quickly and reliably

Key mocked components:
- `yfinance.Ticker`: Yahoo Finance API calls
- File system operations: `open()`, `os.path.exists()`, etc.
- JSON operations: `json.load()`, `json.dump()`

## Adding New Tests

When adding new tests:

1. Follow the existing naming convention: `test_<functionality>`
2. Use descriptive docstrings for test methods
3. Group related tests in classes (e.g., `TestAsset`, `TestAssetData`)
4. Mock external dependencies appropriately
5. Test both success and failure scenarios
6. Include integration tests for complex workflows

## Test Configuration

The test configuration is defined in `pytest.ini`:
- Test discovery patterns
- Output formatting
- Test paths

## Continuous Integration

These tests are designed to run in CI/CD environments and should:
- Run quickly (< 2 seconds for the full suite)
- Be deterministic (no flaky tests)
- Not require external network access
- Clean up any temporary files created during testing
