import pytest
from unittest.mock import Mock, patch
from services.data_provider import DataProvider, AssetDataResponse, DataProviderFactory
from services.yfinance import YFinanceClient


class MockDataProvider(DataProvider):
    """Mock data provider for testing"""
    
    @property
    def name(self) -> str:
        return "Mock Provider"
    
    def get_asset_data(self, symbol: str) -> AssetDataResponse:
        return AssetDataResponse(
            symbol=symbol,
            price=100.0,
            sectors={"Technology": 0.8, "Healthcare": 0.2},
            geography={"US": 0.9, "International": 0.1},
            top_holdings={"AAPL": 0.1, "MSFT": 0.08},
            quote_type="ETF"
        )


class TestAssetDataResponse:
    """Test the AssetDataResponse dataclass"""
    
    def test_asset_data_response_creation_minimal(self):
        """Test creating AssetDataResponse with minimal required fields"""
        response = AssetDataResponse(symbol="AAPL", price=150.0)
        assert response.symbol == "AAPL"
        assert response.price == 150.0
        assert response.sectors is None
        assert response.geography is None
        assert response.top_holdings is None
        assert response.quote_type == 'EQUITY'
        assert response.success is True
        assert response.error_message is None
    
    def test_asset_data_response_creation_full(self):
        """Test creating AssetDataResponse with all fields"""
        sectors = {"Technology": 0.8, "Healthcare": 0.2}
        geography = {"US": 0.9, "International": 0.1}
        holdings = {"AAPL": 0.1, "MSFT": 0.08}
        
        response = AssetDataResponse(
            symbol="VTI",
            price=220.50,
            sectors=sectors,
            geography=geography,
            top_holdings=holdings,
            quote_type="ETF",
            success=True,
            error_message=None
        )
        
        assert response.symbol == "VTI"
        assert response.price == 220.50
        assert response.sectors == sectors
        assert response.geography == geography
        assert response.top_holdings == holdings
        assert response.quote_type == "ETF"
        assert response.success is True
        assert response.error_message is None
    
    def test_asset_data_response_error(self):
        """Test creating AssetDataResponse for error case"""
        response = AssetDataResponse(
            symbol="INVALID",
            price=0.0,
            success=False,
            error_message="Symbol not found"
        )
        
        assert response.symbol == "INVALID"
        assert response.price == 0.0
        assert response.success is False
        assert response.error_message == "Symbol not found"


class TestDataProviderFactory:
    """Test the DataProviderFactory"""
    
    def setup_method(self):
        """Set up test fixtures before each test method"""
        # Clear any existing providers
        DataProviderFactory._providers.clear()
    
    def test_register_provider(self):
        """Test registering a data provider"""
        DataProviderFactory.register_provider("mock", MockDataProvider)
        assert "mock" in DataProviderFactory._providers
        assert DataProviderFactory._providers["mock"] == MockDataProvider
    
    def test_create_provider(self):
        """Test creating a provider instance"""
        DataProviderFactory.register_provider("mock", MockDataProvider)
        provider = DataProviderFactory.create_provider("mock")
        
        assert isinstance(provider, MockDataProvider)
        assert provider.name == "Mock Provider"
    
    def test_create_unknown_provider(self):
        """Test creating an unknown provider raises error"""
        with pytest.raises(ValueError, match="Unknown data provider: unknown"):
            DataProviderFactory.create_provider("unknown")
    
    def test_get_available_providers(self):
        """Test getting list of available providers"""
        DataProviderFactory.register_provider("mock1", MockDataProvider)
        DataProviderFactory.register_provider("mock2", MockDataProvider)
        
        providers = DataProviderFactory.get_available_providers()
        assert "mock1" in providers
        assert "mock2" in providers
        assert len(providers) == 2


class TestYFinanceClient:
    """Test the YFinanceClient"""
    
    def test_name_property(self):
        """Test the name property"""
        client = YFinanceClient()
        assert client.name == "Yahoo Finance"
    
    @patch('yfinance.Ticker')
    def test_get_asset_data_success(self, mock_ticker_class):
        """Test successful asset data retrieval"""
        # Mock the ticker and its methods
        mock_ticker = Mock()
        mock_fast_info = Mock()
        mock_fast_info.last_price = 150.0
        mock_ticker.get_fast_info.return_value = mock_fast_info
        
        # Mock info for quote type
        mock_ticker.info = {'quoteType': 'ETF'}
        
        # Mock funds_data for additional data
        mock_funds_data = Mock()
        mock_sector_weightings = {"Technology": 0.8}
        mock_funds_data.sector_weightings = mock_sector_weightings
        mock_ticker.get_funds_data.return_value = mock_funds_data
        mock_ticker.funds_data = mock_funds_data
        
        mock_ticker_class.return_value = mock_ticker
        
        client = YFinanceClient()
        response = client.get_asset_data("AAPL")
        
        assert response.success is True
        assert response.symbol == "AAPL"
        assert response.price == 150.0
        assert response.quote_type == "ETF"
        assert response.sectors == {"Technology": 0.8}
        assert response.error_message is None
    
    @patch('yfinance.Ticker')
    def test_get_asset_data_failure(self, mock_ticker_class):
        """Test asset data retrieval failure"""
        mock_ticker_class.side_effect = Exception("Network error")
        
        client = YFinanceClient()
        response = client.get_asset_data("AAPL")
        
        assert response.success is False
        assert response.symbol == "AAPL"
        assert response.price == 0.0
        assert "Network error" in response.error_message
    
    @patch('yfinance.Ticker')
    def test_get_quote_type_from_info(self, mock_ticker_class):
        """Test getting quote type from info"""
        mock_ticker = Mock()
        mock_ticker.info = {'quoteType': 'MUTUALFUND'}
        mock_ticker.get_fast_info.return_value = Mock(last_price=100.0)
        mock_ticker_class.return_value = mock_ticker
        
        client = YFinanceClient()
        quote_type = client._get_quote_type(mock_ticker)
        
        assert quote_type == 'MUTUALFUND'
    
    def test_get_quote_type_fallback(self):
        """Test quote type fallback to EQUITY"""
        mock_ticker = Mock()
        mock_ticker.info = {}
        # Make funds_data access raise an exception
        type(mock_ticker).funds_data = property(lambda self: (_ for _ in ()).throw(Exception("No funds data")))

        client = YFinanceClient()
        quote_type = client._get_quote_type(mock_ticker)

        assert quote_type == 'EQUITY'
    
    def test_is_available(self):
        """Test provider availability check"""
        client = YFinanceClient()
        assert client.is_available() is True


class TestDataProviderIntegration:
    """Integration tests for the data provider system"""

    def test_yfinance_provider_registration(self):
        """Test that YFinance provider is properly registered"""
        # Save current state
        original_providers = DataProviderFactory._providers.copy()

        try:
            # Clear and manually register the provider
            DataProviderFactory._providers.clear()
            from services.yfinance import YFinanceClient
            DataProviderFactory.register_provider("yfinance", YFinanceClient)

            providers = DataProviderFactory.get_available_providers()
            assert "yfinance" in providers

            provider = DataProviderFactory.create_provider("yfinance")
            assert isinstance(provider, YFinanceClient)
            assert provider.name == "Yahoo Finance"
        finally:
            # Restore original state
            DataProviderFactory._providers = original_providers
