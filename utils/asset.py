import pandas as pd
import os
import json
from dataclasses import dataclass
from typing import Optional, <PERSON><PERSON>
from enum import IntEnum
from services.data_provider import DataProviderFactory


class StyleClassification(IntEnum):
    """Investment style classification"""
    VALUE = 0
    BLEND = 1
    GROWTH = 2


class MarketCapClassification(IntEnum):
    """Market capitalization classification"""
    SMALL = 0
    MEDIUM = 1
    LARGE = 2

@dataclass
class AssetData:
    symbol: str
    price: float
    sectors: Optional[dict] = None
    geography: Optional[dict] = None
    top_holdings: Optional[dict] = None
    quote_type: str = 'EQUITY'
    asset_type: Optional[Tuple[int, int]] = None  # [style, market_cap] where style: 0=Value, 1=Blend, 2=Growth; market_cap: 0=Small, 1=Medium, 2=Large

class Asset:
    """
    A single asset in the portfolio.
    """
    def __init__(self, sym, quantity=None, data_provider="yfinance"):
        self.base_quantity = quantity
        self.data_provider_name = data_provider
        self.data = AssetData(
            symbol=sym,
            price=0,
            sectors=None,
            geography=None,
            top_holdings=None,
            asset_type=None
        )
        self.simulations = []

        self._get_or_update_data(from_cache=True)
    
    def add_simulation(self, name, quantity):
        """Add a simulation to the asset"""
        pass

    def get_style_classification(self) -> Optional[StyleClassification]:
        """Get the investment style classification (Value/Blend/Growth)"""
        if self.data.asset_type:
            return StyleClassification(self.data.asset_type[0])
        return None

    def get_market_cap_classification(self) -> Optional[MarketCapClassification]:
        """Get the market cap classification (Small/Medium/Large)"""
        if self.data.asset_type:
            return MarketCapClassification(self.data.asset_type[1])
        return None

    def get_grid_position(self) -> Optional[Tuple[int, int]]:
        """Get the [x, y] grid position where x=style, y=market_cap"""
        return self.data.asset_type

    def get_style_name(self) -> Optional[str]:
        """Get the human-readable style name"""
        style = self.get_style_classification()
        if style is not None:
            return style.name.title()
        return None

    def get_market_cap_name(self) -> Optional[str]:
        """Get the human-readable market cap name"""
        market_cap = self.get_market_cap_classification()
        if market_cap is not None:
            return market_cap.name.title()
        return None

    @staticmethod
    def create_asset_type_grid(assets: list, weights: Optional[list] = None) -> dict:
        """
        Create a weighted grid of assets based on their asset type classifications

        Args:
            assets: List of Asset objects
            weights: Optional list of weights for each asset (defaults to equal weights)

        Returns:
            dict: Grid representation with weighted values
                {
                    'grid': 3x3 numpy array with weighted values,
                    'labels': dict with axis labels,
                    'asset_counts': dict with count of assets in each cell
                }
        """
        import numpy as np

        if not assets:
            return {
                'grid': np.zeros((3, 3)),
                'labels': {
                    'x_labels': ['Value', 'Blend', 'Growth'],
                    'y_labels': ['Small', 'Medium', 'Large']
                },
                'asset_counts': {}
            }

        # Initialize grid and counts
        grid = np.zeros((3, 3))  # [market_cap, style]
        asset_counts = {}

        # Default to equal weights if not provided
        if weights is None:
            weights = [1.0] * len(assets)
        elif len(weights) != len(assets):
            raise ValueError("Number of weights must match number of assets")

        # Populate grid with weighted values
        for asset, weight in zip(assets, weights):
            if asset.data.asset_type:
                style, market_cap = asset.data.asset_type
                grid[market_cap, style] += weight

                # Track asset counts
                cell_key = (market_cap, style)
                if cell_key not in asset_counts:
                    asset_counts[cell_key] = []
                asset_counts[cell_key].append(asset.data.symbol)

        return {
            'grid': grid,
            'labels': {
                'x_labels': ['Value', 'Blend', 'Growth'],
                'y_labels': ['Small', 'Medium', 'Large']
            },
            'asset_counts': asset_counts
        }
    
    def write_to_cache(self):
        """Write symbol data to cache"""
        if self.data is None:
            return False
        
        os.makedirs('.cache', exist_ok=True)
        cache_path = f'.cache/{self.data.symbol}.json'
        with open(cache_path, 'w') as f:
            # Convert AssetData to dict for JSON serialization
            data_dict = {
                'symbol': self.data.symbol,
                'price': self.data.price,
                'sectors': self.data.sectors,
                'geography': self.data.geography,
                'top_holdings': self.data.top_holdings,
                'quote_type': self.data.quote_type,
                'asset_type': self.data.asset_type
            }
            json.dump(data_dict, f, indent=2)
        return True
    
    def _get_or_update_data(self, from_cache=True):
        """Get data from cache or fetch from external data provider"""
        if from_cache and self._load_from_cache():
            return self.data

        if self._load_from_external_provider():
            self.write_to_cache()
            return self.data

        return None

    def _load_from_cache(self):
        """Load symbol data from cache"""
        cache_path = f'.cache/{self.data.symbol}.json'
        if os.path.exists(cache_path):
            with open(cache_path, 'r') as f:
                data_dict = json.load(f)
                self.data = AssetData(
                    symbol=data_dict['symbol'],
                    price=data_dict['price'],
                    sectors=data_dict.get('sectors'),
                    geography=data_dict.get('geography'),
                    top_holdings=data_dict.get('top_holdings'),
                    quote_type=data_dict.get('quote_type', 'EQUITY'),
                    asset_type=data_dict.get('asset_type')
                )
            return True
        return False
        
    def _load_from_external_provider(self):
        """Fetch data from external data provider and populate symbol data"""
        try:
            # Get the data provider
            provider = DataProviderFactory.create_provider(self.data_provider_name)

            # Fetch data
            response = provider.get_asset_data(self.data.symbol)

            if not response.success:
                print(response.error_message)
                return False

            # Update the asset data with the response
            self.data = AssetData(
                symbol=response.symbol,
                price=response.price,
                sectors=response.sectors,
                geography=response.geography,
                top_holdings=response.top_holdings,
                quote_type=response.quote_type,
                asset_type=response.asset_type
            )

            return True
        except Exception as e:
            print(f"Error fetching data for {self.data.symbol}: {e}")
            return False
