"""
Global state management for the portfolio simulator application.
Manages the global assets list and provides thread-safe access.
"""

import threading
from typing import List, Optional
from utils.asset import Asset

# Global state
_assets: List[Asset] = []
_cash: float = 0.0
_lock = threading.Lock()

def get_assets() -> List[Asset]:
    """Get a copy of the current assets list."""
    with _lock:
        return _assets.copy()

def set_assets(assets: List[Asset]) -> None:
    """Set the global assets list."""
    with _lock:
        _assets.clear()
        _assets.extend(assets)

def add_asset(asset: Asset) -> None:
    """Add a single asset to the global list."""
    with _lock:
        _assets.append(asset)

def clear_assets() -> None:
    """Clear all assets from the global list."""
    with _lock:
        _assets.clear()

def get_asset_count() -> int:
    """Get the number of assets in the global list."""
    with _lock:
        return len(_assets)

def get_asset_by_symbol(symbol: str) -> Optional[Asset]:
    """Get an asset by its symbol."""
    with _lock:
        for asset in _assets:
            if asset.symbol == symbol:
                return asset
        return None

def update_asset(symbol: str, asset: Asset) -> bool:
    """Update an existing asset by symbol. Returns True if found and updated."""
    with _lock:
        for i, existing_asset in enumerate(_assets):
            if existing_asset.symbol == symbol:
                _assets[i] = asset
                return True
        return False

def remove_asset(symbol: str) -> bool:
    """Remove an asset by symbol. Returns True if found and removed."""
    with _lock:
        for i, asset in enumerate(_assets):
            if asset.symbol == symbol:
                del _assets[i]
                return True
        return False

def get_cash() -> float:
    """Get the current cash amount."""
    with _lock:
        return _cash

def set_cash(cash: float) -> None:
    """Set the cash amount."""
    with _lock:
        global _cash
        _cash = cash

def add_cash(amount: float) -> None:
    """Add to the cash amount."""
    with _lock:
        global _cash
        _cash += amount

def clear_cash() -> None:
    """Clear the cash amount."""
    with _lock:
        global _cash
        _cash = 0.0

def has_portfolio_data() -> bool:
    """Check if there is any portfolio data (assets or cash)."""
    with _lock:
        return len(_assets) > 0 or _cash > 0
