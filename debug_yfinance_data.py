#!/usr/bin/env python3
"""
Debug script to explore what data is available from yfinance for ETFs
"""

import yfinance as yf


def debug_ticker_data(symbol):
    """Debug what data is available for a ticker"""
    print(f"\n=== Debugging {symbol} ===")
    
    ticker = yf.Ticker(symbol)
    
    # Check basic info
    try:
        info = ticker.info
        print(f"Info keys: {list(info.keys())[:10]}...")  # Show first 10 keys
        print(f"Quote Type: {info.get('quoteType', 'Not found')}")
        if 'marketCap' in info:
            print(f"Market Cap: {info['marketCap']:,}")
    except Exception as e:
        print(f"Error getting info: {e}")
    
    # Check funds data
    try:
        funds_data = ticker.get_funds_data()
        if funds_data:
            print(f"Funds data type: {type(funds_data)}")
            print(f"Funds data attributes: {dir(funds_data)}")
            
            # Check for funds_overview
            if hasattr(funds_data, 'funds_overview'):
                funds_overview = funds_data.funds_overview
                print(f"Funds overview type: {type(funds_overview)}")
                print(f"Funds overview: {funds_overview}")
            else:
                print("No funds_overview attribute found")
                
            # Check for other relevant attributes
            for attr in ['overview', 'fund_overview', 'category', 'categoryName']:
                if hasattr(funds_data, attr):
                    value = getattr(funds_data, attr)
                    print(f"{attr}: {value}")
        else:
            print("No funds data available")
    except Exception as e:
        print(f"Error getting funds data: {e}")


def main():
    # Test with different types of assets
    test_symbols = [
        "VTI",   # Total Stock Market ETF
        "VTV",   # Value ETF  
        "VUG",   # Growth ETF
        "VB",    # Small Cap ETF
        "AAPL",  # Individual stock
    ]
    
    for symbol in test_symbols:
        try:
            debug_ticker_data(symbol)
        except Exception as e:
            print(f"Error debugging {symbol}: {e}")


if __name__ == "__main__":
    main()
