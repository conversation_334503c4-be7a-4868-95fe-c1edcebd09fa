#!/usr/bin/env python3
"""
Test script to verify the new portfolio chart functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import yfinance service to ensure it's registered
import services.yfinance
from utils.asset import Asset
from pages.portfolio import create_pie_chart, create_bar_chart, create_asset_type_grid_chart


def test_chart_functions():
    """Test the chart creation functions with sample data"""
    print("=== Testing Chart Creation Functions ===\n")
    
    # Test data
    sample_sector_data = {
        'Technology': 50000,
        'Healthcare': 30000,
        'Financial Services': 20000,
        'Consumer Cyclical': 15000,
        'Energy': 10000
    }
    
    sample_quote_type_data = {
        'EQUITY': 75000,
        'ETF': 40000,
        'MUTUALFUND': 10000
    }
    
    sample_country_data = {
        'United States': 100000,
        'Japan': 15000,
        'United Kingdom': 10000
    }
    
    # Test pie chart
    print("1. Testing pie chart creation...")
    try:
        pie_fig = create_pie_chart(sample_quote_type_data, "Quote Type Test")
        print(f"   ✓ Pie chart created successfully")
        print(f"   Data points: {len(pie_fig['data'])}")
    except Exception as e:
        print(f"   ✗ Error creating pie chart: {e}")
    
    # Test bar chart
    print("\n2. Testing bar chart creation...")
    try:
        bar_fig = create_bar_chart(sample_sector_data, "Sector Test")
        print(f"   ✓ Bar chart created successfully")
        print(f"   Data points: {len(bar_fig['data'])}")
    except Exception as e:
        print(f"   ✗ Error creating bar chart: {e}")
    
    # Test asset type grid with sample assets
    print("\n3. Testing asset type grid creation...")
    try:
        # Create some sample assets (these won't fetch real data)
        sample_assets = []
        sample_values = []
        
        # Create mock assets with different classifications
        for i, (symbol, asset_type, value) in enumerate([
            ("AAPL", (1, 2), 25000),  # Blend, Large
            ("VTI", (1, 1), 30000),   # Blend, Medium  
            ("VTV", (0, 2), 20000),   # Value, Large
            ("VUG", (2, 2), 15000),   # Growth, Large
            ("VB", (1, 0), 10000),    # Blend, Small
        ]):
            asset = Asset(symbol)
            # Manually set the asset type for testing
            asset.data.asset_type = asset_type
            asset.data.price = value / 100  # Mock price
            sample_assets.append(asset)
            sample_values.append(value)
        
        grid_fig = create_asset_type_grid_chart(sample_assets, sample_values)
        print(f"   ✓ Asset type grid created successfully")
        print(f"   Grid shape: {grid_fig['data'][0]['z'].shape if grid_fig['data'] else 'No data'}")
        
        # Print grid values for verification
        if grid_fig['data']:
            grid_values = grid_fig['data'][0]['z']
            print(f"   Grid values:")
            print(f"     Small:  {grid_values[0]}")
            print(f"     Medium: {grid_values[1]}")
            print(f"     Large:  {grid_values[2]}")
            
    except Exception as e:
        print(f"   ✗ Error creating asset type grid: {e}")
        import traceback
        traceback.print_exc()


def test_asset_classification():
    """Test asset classification functionality"""
    print("\n=== Testing Asset Classification ===\n")
    
    test_symbols = ["AAPL", "MSFT"]  # Test with stocks that should work
    
    for symbol in test_symbols:
        print(f"Testing {symbol}...")
        try:
            asset = Asset(symbol)
            print(f"   Quote Type: {asset.data.quote_type}")
            print(f"   Asset Type: {asset.data.asset_type}")
            if asset.data.asset_type:
                print(f"   Style: {asset.get_style_name()}")
                print(f"   Market Cap: {asset.get_market_cap_name()}")
            print()
        except Exception as e:
            print(f"   Error: {e}")
            print()


if __name__ == "__main__":
    test_chart_functions()
    test_asset_classification()
