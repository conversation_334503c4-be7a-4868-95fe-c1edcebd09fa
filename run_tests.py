#!/usr/bin/env python3
"""
Simple test runner script for the portfolio simulator.
Run this script to execute all tests with coverage reporting.
"""

import subprocess
import sys
import os

def run_tests():
    """Run all tests with pytest"""
    print("Running Asset class tests...")
    
    # Change to the project directory
    project_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(project_dir)
    
    # Run pytest with verbose output
    cmd = ["poetry", "run", "pytest", "tests/", "-v", "--tb=short"]
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=False)
        print("\n✅ All tests passed!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"\n❌ Tests failed with return code {e.returncode}")
        return False
    except FileNotFoundError:
        print("❌ Poetry not found. Please install Poetry or run tests manually with:")
        print("   pytest tests/ -v")
        return False

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
