#!/usr/bin/env python3
"""
Example script demonstrating the new asset type classification functionality.

This script shows how to:
1. Create assets with asset type classification
2. Access style and market cap classifications
3. Create weighted grids for portfolio visualization
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.asset import Asset, StyleClassification, MarketCapClassification
import numpy as np

# Import yfinance service to ensure it's registered
import services.yfinance


def print_asset_classification(asset):
    """Print detailed classification information for an asset"""
    print(f"\n=== {asset.data.symbol} ===")
    print(f"Price: ${asset.data.price:.2f}")
    print(f"Quote Type: {asset.data.quote_type}")
    
    if asset.data.asset_type:
        style, market_cap = asset.data.asset_type
        print(f"Asset Type: [{style}, {market_cap}]")
        print(f"Style: {asset.get_style_name()} ({style})")
        print(f"Market Cap: {asset.get_market_cap_name()} ({market_cap})")
        print(f"Grid Position: {asset.get_grid_position()}")
    else:
        print("Asset Type: Not classified")


def demonstrate_grid_creation(assets, weights=None):
    """Demonstrate creating a weighted asset type grid"""
    print("\n=== Asset Type Grid ===")
    
    grid_data = Asset.create_asset_type_grid(assets, weights)
    grid = grid_data['grid']
    labels = grid_data['labels']
    asset_counts = grid_data['asset_counts']
    
    print("\nWeighted Grid:")
    print("Rows: Market Cap (Small, Medium, Large)")
    print("Cols: Style (Value, Blend, Growth)")
    print(grid)
    
    print("\nAsset Distribution:")
    for (market_cap, style), symbols in asset_counts.items():
        market_cap_name = MarketCapClassification(market_cap).name.title()
        style_name = StyleClassification(style).name.title()
        print(f"{market_cap_name} Cap {style_name}: {', '.join(symbols)}")


def main():
    print("=== Asset Type Classification Demo ===\n")
    
    # Test with a variety of assets
    test_symbols = [
        "AAPL",    # Large cap stock
        "VTI",     # Total market ETF
        "VTV",     # Value ETF
        "VUG",     # Growth ETF
        "VB",      # Small cap ETF
        "MSFT",    # Large cap stock
    ]
    
    assets = []
    
    for symbol in test_symbols:
        print(f"Creating asset for {symbol}...")
        try:
            asset = Asset(symbol)
            assets.append(asset)
            print_asset_classification(asset)
        except Exception as e:
            print(f"Error creating asset for {symbol}: {e}")
            print("(This is expected if not connected to internet)")
    
    if assets:
        # Demonstrate grid creation with equal weights
        print("\n" + "="*50)
        demonstrate_grid_creation(assets)
        
        # Demonstrate grid creation with custom weights
        print("\n" + "="*50)
        print("=== Custom Weighted Grid ===")
        # Give more weight to the first few assets
        custom_weights = [2.0, 1.5, 1.0] + [0.5] * (len(assets) - 3)
        custom_weights = custom_weights[:len(assets)]  # Trim to match assets
        demonstrate_grid_creation(assets, custom_weights)
    
    print("\n=== Classification Enums ===")
    print("Style Classifications:")
    for style in StyleClassification:
        print(f"  {style.value}: {style.name.title()}")
    
    print("\nMarket Cap Classifications:")
    for market_cap in MarketCapClassification:
        print(f"  {market_cap.value}: {market_cap.name.title()}")


if __name__ == "__main__":
    main()
