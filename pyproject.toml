[tool.poetry]
name = "portfolio-simulator"
version = "0.1.0"
description = "Simulate and analyze various portfolio allocations"
authors = ["<PERSON> Wolf"]
package-mode = false

[tool.poetry.dependencies]
python = "^3.11"
dash = "^3.0.0"
dash_bootstrap_components = "^2.0.0"
yfinance = "^0.2.0"
pandas = "^2.0.0"
plotly = "^6.0.0"
ipykernel = "^6.30.0"

[tool.poetry.group.dev.dependencies]
pytest = "^8.4.1"
pytest-mock = "^3.14.1"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
