#!/usr/bin/env python3
"""
Test script to verify the portfolio UI functionality.
"""

import base64
import io
from pages.portfolio import parse_input_csv, ingest_portfolio
from utils import global_state

def test_portfolio_functions():
    print("=== Testing Portfolio UI Functions ===\n")
    
    # Test CSV content
    csv_content = """Symbol,Quantity
AAPL,10
MSFT,5
GOOGL,3"""
    
    # Encode as base64 (simulating file upload)
    encoded_content = base64.b64encode(csv_content.encode('utf-8')).decode('utf-8')
    contents = f"data:text/csv;base64,{encoded_content}"
    filename = "test_portfolio.csv"
    
    print("1. Testing parse_input_csv...")
    try:
        df = parse_input_csv(contents, filename)
        print(f"✓ Parsed {len(df)} rows successfully")
        print(f"  Symbols: {list(df.index)}")
        print(f"  Quantities: {list(df['Quantity'])}")
    except Exception as e:
        print(f"❌ Parse failed: {e}")
        return
    
    print("\n2. Testing ingest_portfolio...")
    try:
        # Clear global state first
        global_state.clear_assets()
        
        assets, error = ingest_portfolio(contents, filename)
        
        if error:
            print(f"❌ Ingest failed: {error}")
            return
        
        print(f"✓ Ingested {len(assets)} assets successfully")
        print(f"  Global state has {global_state.get_asset_count()} assets")
        
        # Verify global state
        for asset in assets:
            global_asset = global_state.get_asset_by_symbol(asset.symbol)
            if global_asset:
                print(f"  ✓ {asset.symbol}: {asset.quantity} shares")
            else:
                print(f"  ❌ {asset.symbol} not found in global state")
        
    except Exception as e:
        print(f"❌ Ingest failed: {e}")
        return
    
    print("\n3. Testing error handling...")
    try:
        # Test with invalid CSV
        bad_csv = "Invalid,CSV\nContent,Here"
        bad_encoded = base64.b64encode(bad_csv.encode('utf-8')).decode('utf-8')
        bad_contents = f"data:text/csv;base64,{bad_encoded}"
        
        assets, error = ingest_portfolio(bad_contents, "bad.csv")
        
        if error:
            print(f"✓ Error handling works: {error}")
        else:
            print("❌ Should have failed with invalid CSV")
            
    except Exception as e:
        print(f"✓ Exception handling works: {e}")
    
    print("\n🎉 Portfolio UI functions test complete!")

if __name__ == "__main__":
    try:
        test_portfolio_functions()
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # Clean up
        global_state.clear_assets()
