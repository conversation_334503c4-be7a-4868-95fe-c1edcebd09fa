#!/usr/bin/env python3
"""
Example usage of the refactored Asset class with modular data providers.

This demonstrates how the Asset class now uses a pluggable data provider system
that makes it easy to swap between different data sources in the future.
"""

from utils.asset import Asset
from services.data_provider import DataProviderFactory
from services.yfinance import YFinanceClient


def main():
    print("=== Asset Class with Modular Data Providers ===\n")
    
    # Example 1: Using default yfinance provider
    print("1. Creating Asset with default yfinance provider:")
    try:
        asset1 = Asset("AAPL", quantity=10)
        print(f"   Symbol: {asset1.data.symbol}")
        print(f"   Price: ${asset1.data.price:.2f}")
        print(f"   Quote Type: {asset1.data.quote_type}")
        print(f"   Data Provider: {asset1.data_provider_name}")
        if asset1.data.sectors:
            print(f"   Sectors: {asset1.data.sectors}")
        if asset1.data.geography:
            print(f"   Geography: {asset1.data.geography}")
        print()
    except Exception as e:
        print(f"   Error: {e}")
        print("   (This is expected if not connected to internet)\n")
    
    # Example 2: Explicitly specifying yfinance provider
    print("2. Creating Asset with explicitly specified yfinance provider:")
    try:
        asset2 = Asset("VTI", data_provider="yfinance")
        print(f"   Symbol: {asset2.data.symbol}")
        print(f"   Price: ${asset2.data.price:.2f}")
        print(f"   Quote Type: {asset2.data.quote_type}")
        print(f"   Data Provider: {asset2.data_provider_name}")
        if asset2.data.sectors:
            print(f"   Sectors: {asset2.data.sectors}")
        if asset2.data.geography:
            print(f"   Geography: {asset2.data.geography}")
        if asset2.data.top_holdings:
            print(f"   Top Holdings: {list(asset2.data.top_holdings.keys())[:3]}...")
        print()
    except Exception as e:
        print(f"   Error: {e}")
        print("   (This is expected if not connected to internet)\n")
    
    # Example 3: Demonstrating the factory pattern
    print("3. Using DataProviderFactory directly:")
    try:
        # Get available providers
        providers = DataProviderFactory.get_available_providers()
        print(f"   Available providers: {providers}")
        
        # Create a provider instance
        if "yfinance" in providers:
            provider = DataProviderFactory.create_provider("yfinance")
            print(f"   Created provider: {provider.name}")
            
            # Fetch data directly
            response = provider.get_asset_data("MSFT")
            if response.success:
                print(f"   MSFT Price: ${response.price:.2f}")
                print(f"   Quote Type: {response.quote_type}")
            else:
                print(f"   Error: {response.error_message}")
        print()
    except Exception as e:
        print(f"   Error: {e}")
        print("   (This is expected if not connected to internet)\n")
    
    # Example 4: Demonstrating extensibility
    print("4. Demonstrating extensibility:")
    print("   The system is designed to easily add new data providers.")
    print("   To add a new provider (e.g., Alpha Vantage):")
    print("   1. Create a class that inherits from DataProvider")
    print("   2. Implement the required methods (name, get_asset_data)")
    print("   3. Register it with DataProviderFactory.register_provider()")
    print("   4. Use it by passing the provider name to Asset()")
    print()
    
    print("=== Key Benefits of the Refactored System ===")
    print("✓ Modular: Easy to swap data providers")
    print("✓ Extensible: Simple to add new data sources")
    print("✓ Testable: Clean separation of concerns")
    print("✓ Comprehensive: Fetches sectors, geography, and holdings data")
    print("✓ Robust: Proper error handling and fallbacks")


if __name__ == "__main__":
    main()
